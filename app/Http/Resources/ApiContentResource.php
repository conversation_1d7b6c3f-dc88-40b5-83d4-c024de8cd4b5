<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ApiContentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'status' => $this->status,
            'content_type' => $this->getContentTypeName(),
            'contentable_type' => $this->contentable_type,
            'contentable_id' => $this->contentable_id,
            'category_id' => $this->category_id,
            'category' => [
                'id' => $this->category->id,
                'name' => $this->category->name,
                'status' => $this->category->status,
                'parent_id' => $this->category->parent_id,
            ],
            'contentable' => $this->formatContentableData(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }

    /**
     * Format the contentable data based on its type.
     *
     * @return array|null
     */
    private function formatContentableData(): ?array
    {
        if (!$this->contentable) {
            return null;
        }

        $baseData = [
            'id' => $this->contentable->id,
            'category_id' => $this->contentable->category_id,
            'created_at' => $this->contentable->created_at?->toISOString(),
            'updated_at' => $this->contentable->updated_at?->toISOString(),
        ];

        return match ($this->contentable_type) {
            'App\\Models\\Audio' => array_merge($baseData, [
                'audio_file' => $this->contentable->audio_file,
                'audio_url' => $this->contentable->audio_file ? asset('storage/' . $this->contentable->audio_file) : null,
            ]),
            'App\\Models\\Video' => array_merge($baseData, [
                'youtube_video_id' => $this->contentable->youtube_video_id,
                'duration' => $this->contentable->duration,
                'embed_url' => $this->contentable->embed_url,
                'watch_url' => $this->contentable->watch_url,
                'thumbnail_url' => $this->contentable->thumbnail_url,
                'thumbnail_medium_url' => $this->contentable->thumbnail_medium_url,
            ]),
            'App\\Models\\Book' => array_merge($baseData, [
                'pages_count' => $this->contentable->pages_count,
                'published_date' => $this->contentable->published_date?->toDateString(),
                'publisher' => $this->contentable->publisher,
                'cover_image' => $this->contentable->cover_image,
                'cover_image_url' => $this->contentable->cover_image_url,
                'file' => $this->contentable->file,
                'file_url' => $this->contentable->file_url,
            ]),
            default => $baseData,
        };
    }
}
