<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Book extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'pages_count',
        'published_date',
        'publisher',
        'cover_image',
        'file',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'category_id' => 'integer',
            'pages_count' => 'integer',
            'published_date' => 'date',
        ];
    }

    /**
     * Get the category that owns the book.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the content record associated with this book.
     */
    public function content(): MorphOne
    {
        return $this->morphOne(Content::class, 'contentable');
    }

    /**
     * Get the cover image URL.
     */
    public function coverImageUrl(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->cover_image ? asset('storage/' . $this->cover_image) : null
        );
    }

    /**
     * Get the book file URL.
     */
    public function fileUrl(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->file ? asset('storage/' . $this->file) : null
        );
    }

    /**
     * Scope a query to filter by category.
     */
    public function scopeInCategory($query, int $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Check if the book has a cover image.
     */
    public function hasCoverImage(): bool
    {
        return !empty($this->cover_image);
    }

    /**
     * Check if the book has a file.
     */
    public function hasFile(): bool
    {
        return !empty($this->file);
    }
}
