<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Book>
 */
class BookFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'pages_count' => fake()->numberBetween(50, 1000),
            'published_date' => fake()->dateTimeBetween('-10 years', 'now')->format('Y-m-d'),
            'publisher' => fake()->company(),
            'cover_image' => fake()->boolean(70) ? 'books/covers/' . fake()->uuid() . '.jpg' : null,
            'file' => fake()->boolean(60) ? 'books/files/' . fake()->uuid() . '.pdf' : null,
            'category_id' => Category::factory(),
        ];
    }

    /**
     * Create a book with cover image.
     */
    public function withCover(): static
    {
        return $this->state(fn() => [
            'cover_image' => 'books/covers/' . fake()->uuid() . '.jpg',
        ]);
    }

    /**
     * Create a book with file.
     */
    public function withFile(): static
    {
        return $this->state(fn() => [
            'file' => 'books/files/' . fake()->uuid() . '.pdf',
        ]);
    }

    /**
     * Create a book with both cover and file.
     */
    public function complete(): static
    {
        return $this->state(fn() => [
            'cover_image' => 'books/covers/' . fake()->uuid() . '.jpg',
            'file' => 'books/files/' . fake()->uuid() . '.pdf',
        ]);
    }
}
