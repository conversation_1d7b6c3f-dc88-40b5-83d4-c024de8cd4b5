<?php

namespace Tests\Feature\Api;

use App\Models\Audio;
use App\Models\Book;
use App\Models\Category;
use App\Models\Content;
use App\Models\Video;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ContentControllerTest extends TestCase
{
    use RefreshDatabase;

    protected Category $category;
    protected Category $inactiveCategory;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test categories
        $this->category = Category::factory()->create([
            'name' => 'Test Category',
            'status' => true,
        ]);

        $this->inactiveCategory = Category::factory()->create([
            'name' => 'Inactive Category',
            'status' => false,
        ]);
    }

    public function test_get_category_contents_returns_success_response()
    {
        // Create test content items
        $audio = Audio::factory()->create(['category_id' => $this->category->id]);
        $video = Video::factory()->create(['category_id' => $this->category->id]);
        $book = Book::factory()->create(['category_id' => $this->category->id]);

        Content::factory()->create([
            'category_id' => $this->category->id,
            'contentable_type' => Audio::class,
            'contentable_id' => $audio->id,
            'status' => true,
        ]);

        Content::factory()->create([
            'category_id' => $this->category->id,
            'contentable_type' => Video::class,
            'contentable_id' => $video->id,
            'status' => true,
        ]);

        Content::factory()->create([
            'category_id' => $this->category->id,
            'contentable_type' => Book::class,
            'contentable_id' => $book->id,
            'status' => true,
        ]);

        $response = $this->getJson("/api/v1/categories/{$this->category->id}/contents");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'category' => [
                        'id',
                        'name',
                        'status',
                        'parent_id',
                        'parent_name',
                        'total_contents_count',
                        'created_at',
                        'updated_at',
                    ],
                    'contents' => [
                        '*' => [
                            'id',
                            'title',
                            'description',
                            'status',
                            'content_type',
                            'contentable_type',
                            'contentable_id',
                            'category_id',
                            'category' => [
                                'id',
                                'name',
                                'status',
                                'parent_id',
                            ],
                            'contentable' => [
                                'id',
                                'category_id',
                                'created_at',
                                'updated_at',
                            ],
                            'created_at',
                            'updated_at',
                        ]
                    ],
                    'pagination' => [
                        'current_page',
                        'per_page',
                        'total',
                        'last_page',
                        'from',
                        'to',
                        'has_more',
                        'next_page_url',
                        'prev_page_url',
                    ],
                    'content_type_summary' => [
                        'audio_count',
                        'video_count',
                        'book_count',
                        'total_count',
                    ],
                ]
            ]);

        // Verify the response contains our test data
        $responseData = $response->json('data');
        $this->assertEquals($this->category->id, $responseData['category']['id']);
        $this->assertEquals($this->category->name, $responseData['category']['name']);
        $this->assertCount(3, $responseData['contents']);
        $this->assertEquals(3, $responseData['content_type_summary']['total_count']);
        $this->assertEquals(1, $responseData['content_type_summary']['audio_count']);
        $this->assertEquals(1, $responseData['content_type_summary']['video_count']);
        $this->assertEquals(1, $responseData['content_type_summary']['book_count']);
    }

    public function test_get_category_contents_returns_404_for_nonexistent_category()
    {
        $response = $this->getJson('/api/v1/categories/99999/contents');

        $response->assertStatus(404)
            ->assertJson([
                'error' => 'Category not found or inactive',
            ]);
    }

    public function test_get_category_contents_returns_404_for_inactive_category()
    {
        $response = $this->getJson("/api/v1/categories/{$this->inactiveCategory->id}/contents");

        $response->assertStatus(404)
            ->assertJson([
                'error' => 'Category not found or inactive',
            ]);
    }

    public function test_get_category_contents_excludes_inactive_content()
    {
        $audio = Audio::factory()->create(['category_id' => $this->category->id]);

        // Create active content
        Content::factory()->create([
            'category_id' => $this->category->id,
            'contentable_type' => Audio::class,
            'contentable_id' => $audio->id,
            'status' => true,
        ]);

        // Create inactive content
        $inactiveAudio = Audio::factory()->create(['category_id' => $this->category->id]);
        Content::factory()->create([
            'category_id' => $this->category->id,
            'contentable_type' => Audio::class,
            'contentable_id' => $inactiveAudio->id,
            'status' => false,
        ]);

        $response = $this->getJson("/api/v1/categories/{$this->category->id}/contents");

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $this->assertCount(1, $responseData['contents']);
        $this->assertEquals(1, $responseData['content_type_summary']['total_count']);
    }

    public function test_get_category_contents_supports_pagination()
    {
        // Create multiple content items
        for ($i = 0; $i < 25; $i++) {
            $audio = Audio::factory()->create(['category_id' => $this->category->id]);
            Content::factory()->create([
                'category_id' => $this->category->id,
                'contentable_type' => Audio::class,
                'contentable_id' => $audio->id,
                'status' => true,
            ]);
        }

        // Test first page
        $response = $this->getJson("/api/v1/categories/{$this->category->id}/contents?per_page=10&page=1");

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $this->assertCount(10, $responseData['contents']);
        $this->assertEquals(1, $responseData['pagination']['current_page']);
        $this->assertEquals(10, $responseData['pagination']['per_page']);
        $this->assertEquals(25, $responseData['pagination']['total']);
        $this->assertEquals(3, $responseData['pagination']['last_page']);
        $this->assertTrue($responseData['pagination']['has_more']);

        // Test second page
        $response = $this->getJson("/api/v1/categories/{$this->category->id}/contents?per_page=10&page=2");

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $this->assertCount(10, $responseData['contents']);
        $this->assertEquals(2, $responseData['pagination']['current_page']);
    }

    public function test_get_category_contents_validates_pagination_parameters()
    {
        // Test invalid per_page
        $response = $this->getJson("/api/v1/categories/{$this->category->id}/contents?per_page=0");
        $response->assertStatus(422)
            ->assertJsonStructure(['errors']);

        // Test invalid page
        $response = $this->getJson("/api/v1/categories/{$this->category->id}/contents?page=0");
        $response->assertStatus(422)
            ->assertJsonStructure(['errors']);

        // Test per_page too large
        $response = $this->getJson("/api/v1/categories/{$this->category->id}/contents?per_page=101");
        $response->assertStatus(422)
            ->assertJsonStructure(['errors']);
    }

    public function test_get_category_contents_returns_empty_for_category_with_no_content()
    {
        $response = $this->getJson("/api/v1/categories/{$this->category->id}/contents");

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $this->assertCount(0, $responseData['contents']);
        $this->assertEquals(0, $responseData['content_type_summary']['total_count']);
        $this->assertEquals(0, $responseData['content_type_summary']['audio_count']);
        $this->assertEquals(0, $responseData['content_type_summary']['video_count']);
        $this->assertEquals(0, $responseData['content_type_summary']['book_count']);
    }

    public function test_get_category_contents_includes_correct_contentable_data_for_audio()
    {
        $audio = Audio::factory()->create([
            'category_id' => $this->category->id,
            'audio_file' => 'test-audio.mp3',
        ]);

        Content::factory()->create([
            'category_id' => $this->category->id,
            'contentable_type' => Audio::class,
            'contentable_id' => $audio->id,
            'status' => true,
        ]);

        $response = $this->getJson("/api/v1/categories/{$this->category->id}/contents");

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $contentable = $responseData['contents'][0]['contentable'];

        $this->assertEquals('test-audio.mp3', $contentable['audio_file']);
        $this->assertStringContainsString('test-audio.mp3', $contentable['audio_url']);
    }

    public function test_get_category_contents_includes_correct_contentable_data_for_video()
    {
        $video = Video::factory()->create([
            'category_id' => $this->category->id,
            'youtube_video_id' => 'dQw4w9WgXcQ',
            'duration' => '3:32',
        ]);

        Content::factory()->create([
            'category_id' => $this->category->id,
            'contentable_type' => Video::class,
            'contentable_id' => $video->id,
            'status' => true,
        ]);

        $response = $this->getJson("/api/v1/categories/{$this->category->id}/contents");

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $contentable = $responseData['contents'][0]['contentable'];

        $this->assertEquals('dQw4w9WgXcQ', $contentable['youtube_video_id']);
        $this->assertEquals('3:32', $contentable['duration']);
        $this->assertArrayHasKey('embed_url', $contentable);
        $this->assertArrayHasKey('watch_url', $contentable);
        $this->assertArrayHasKey('thumbnail_url', $contentable);
    }

    public function test_get_category_contents_includes_correct_contentable_data_for_book()
    {
        $book = Book::factory()->create([
            'category_id' => $this->category->id,
            'pages_count' => 250,
            'publisher' => 'Test Publisher',
            'cover_image' => 'test-cover.jpg',
            'file' => 'test-book.pdf',
        ]);

        Content::factory()->create([
            'category_id' => $this->category->id,
            'contentable_type' => Book::class,
            'contentable_id' => $book->id,
            'status' => true,
        ]);

        $response = $this->getJson("/api/v1/categories/{$this->category->id}/contents");

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $contentable = $responseData['contents'][0]['contentable'];

        $this->assertEquals(250, $contentable['pages_count']);
        $this->assertEquals('Test Publisher', $contentable['publisher']);
        $this->assertEquals('test-cover.jpg', $contentable['cover_image']);
        $this->assertEquals('test-book.pdf', $contentable['file']);
        $this->assertArrayHasKey('cover_image_url', $contentable);
        $this->assertArrayHasKey('file_url', $contentable);
    }
}
