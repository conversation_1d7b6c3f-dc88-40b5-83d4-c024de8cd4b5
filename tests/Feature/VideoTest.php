<?php

use App\Models\Category;
use App\Models\Video;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('video belongs to a category', function () {
    $category = Category::factory()->create();
    $video = Video::factory()->create(['category_id' => $category->id]);

    expect($video->category->id)->toBe($category->id);
    expect($video->category->name)->toBe($category->name);
});

test('category can have multiple videos', function () {
    $category = Category::factory()->create([
        'name' => 'Test Video Category',
        'status' => true,
    ]);

    $video1 = Video::factory()->create(['category_id' => $category->id]);
    $video2 = Video::factory()->create(['category_id' => $category->id]);

    expect($category->videos)->toHaveCount(2);
    expect($category->videos->first()->id)->toBe($video1->id);
    expect($category->videos->last()->id)->toBe($video2->id);
});

test('video scopes work correctly', function () {
    $videoCategory = Category::factory()->create();

    Video::factory()->create(['category_id' => $videoCategory->id]);

    expect(Video::inCategory($videoCategory->id)->count())->toBe(1);
});

test('youtube url extraction works correctly', function () {
    $testUrls = [
        'https://www.youtube.com/watch?v=dQw4w9WgXcQ' => 'dQw4w9WgXcQ',
        'https://youtu.be/dQw4w9WgXcQ' => 'dQw4w9WgXcQ',
        'https://www.youtube.com/embed/dQw4w9WgXcQ' => 'dQw4w9WgXcQ',
        'https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=10s' => 'dQw4w9WgXcQ',
        'invalid-url' => null,
        'https://vimeo.com/123456' => null,
    ];

    foreach ($testUrls as $url => $expectedId) {
        expect(Video::extractVideoId($url))->toBe($expectedId);
        expect(Video::isValidYouTubeUrl($url))->toBe($expectedId !== null);
    }
});

test('video accessor methods work correctly', function () {
    $video = Video::factory()->create(['youtube_video_id' => 'dQw4w9WgXcQ']);

    expect($video->embed_url)->toBe('https://www.youtube.com/embed/dQw4w9WgXcQ');
    expect($video->watch_url)->toBe('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
    expect($video->thumbnail_url)->toBe('https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg');
    expect($video->thumbnail_medium_url)->toBe('https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg');
});

test('video factory creates valid videos', function () {
    $video = Video::factory()->create();

    expect($video->youtube_video_id)->toBeString();
    expect($video->youtube_video_id)->toHaveLength(11);
    expect($video->category)->toBeInstanceOf(Category::class);
});

test('video validation works with youtube urls', function () {
    $category = Category::factory()->create();

    $video = Video::create([
        'youtube_video_id' => Video::extractVideoId('https://www.youtube.com/watch?v=dQw4w9WgXcQ'),
        'duration' => '3:45',
        'category_id' => $category->id,
    ]);

    expect($video->youtube_video_id)->toBe('dQw4w9WgXcQ');
    expect($video->embed_url)->toBe('https://www.youtube.com/embed/dQw4w9WgXcQ');
});
