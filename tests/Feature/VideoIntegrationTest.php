<?php

use App\Models\Audio;
use App\Models\Category;
use App\Models\Video;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('complete video and audio system integration', function () {
    // Create categories for different types
    $audioCategory = Category::factory()->create([
        'name' => 'Islamic Lectures',
        'status' => true,
    ]);

    $videoCategory = Category::factory()->create([
        'name' => 'Educational Videos',
        'status' => true,
    ]);

    // Create audio and video content
    $audio = Audio::factory()->create();
    $video = Video::factory()->withVideoId('dQw4w9WgXcQ')->create();

    // Test that models can be created without category_id
    expect($audio->audio_file)->toBeString();
    expect($video->youtube_video_id)->toBe('dQw4w9WgXcQ');

    // Test video-specific functionality
    expect($video->embed_url)->toBe('https://www.youtube.com/embed/dQw4w9WgXcQ');
    expect($video->watch_url)->toBe('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
    expect($video->thumbnail_url)->toContain('dQw4w9WgXcQ');

    // Test category hierarchy still works
    expect($audioCategory->name)->toBe('Islamic Lectures');
    expect($videoCategory->name)->toBe('Educational Videos');
});

test('hierarchical category filtering works correctly', function () {
    // Create hierarchical categories
    $mediaParent = Category::factory()->create(['name' => 'Media Content']);
    $audioCategory = Category::factory()->withParent($mediaParent)->create(['name' => 'Audio']);
    $videoCategory = Category::factory()->withParent($mediaParent)->create(['name' => 'Video']);

    // Create content
    Audio::factory()->create();
    Video::factory()->create();

    // Test hierarchical filtering
    expect($mediaParent->subcategories)->toHaveCount(2);
    expect($audioCategory->parent->id)->toBe($mediaParent->id);
    expect($videoCategory->parent->id)->toBe($mediaParent->id);

    // Test that content models were created successfully
    expect($audio->audio_file)->toBeString();
    expect($video->youtube_video_id)->toBeString();
});

test('youtube url processing in real workflow', function () {
    // Simulate form submission with YouTube URL
    $youtubeUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s';
    $extractedId = Video::extractVideoId($youtubeUrl);

    expect($extractedId)->toBe('dQw4w9WgXcQ');
    expect(Video::isValidYouTubeUrl($youtubeUrl))->toBeTrue();

    // Create video with extracted ID
    $video = Video::create([
        'youtube_video_id' => $extractedId,
        'duration' => '3:33',
    ]);

    expect($video->youtube_video_id)->toBe('dQw4w9WgXcQ');
    expect($video->embed_url)->toBe('https://www.youtube.com/embed/dQw4w9WgXcQ');
});
