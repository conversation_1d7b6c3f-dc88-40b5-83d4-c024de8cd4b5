<?php

use App\Models\Audio;
use App\Models\Category;
use App\Models\Video;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('complete video and audio system integration', function () {
    // Create categories for different types
    $audioCategory = Category::factory()->create([
        'name' => 'Islamic Lectures',
        'status' => true,
    ]);

    $videoCategory = Category::factory()->create([
        'name' => 'Educational Videos',
        'status' => true,
    ]);

    // Create audio and video content
    $audio = Audio::factory()->create(['category_id' => $audioCategory->id]);
    $video = Video::factory()->withVideoId('dQw4w9WgXcQ')->create(['category_id' => $videoCategory->id]);

    // Test category relationships
    expect($audioCategory->audio)->toHaveCount(1);
    expect($videoCategory->videos)->toHaveCount(1);
    expect($audioCategory->videos)->toHaveCount(0);
    expect($videoCategory->audio)->toHaveCount(0);

    // Test content relationships
    expect($audio->category->name)->toBe('Islamic Lectures');
    expect($video->category->name)->toBe('Educational Videos');

    // Test scopes work across models
    expect(Audio::inCategory($audioCategory->id)->count())->toBe(1);
    expect(Video::inCategory($videoCategory->id)->count())->toBe(1);

    // Test hierarchical relationships
    expect($audioCategory->audio)->toHaveCount(1);
    expect($videoCategory->videos)->toHaveCount(1);
    expect($audioCategory->videos)->toHaveCount(0);
    expect($videoCategory->audio)->toHaveCount(0);

    // Test video-specific functionality
    expect($video->embed_url)->toBe('https://www.youtube.com/embed/dQw4w9WgXcQ');
    expect($video->watch_url)->toBe('https://www.youtube.com/watch?v=dQw4w9WgXcQ');
    expect($video->thumbnail_url)->toContain('dQw4w9WgXcQ');
});

test('hierarchical category filtering works correctly', function () {
    // Create hierarchical categories
    $mediaParent = Category::factory()->create(['name' => 'Media Content']);
    $audioCategory = Category::factory()->withParent($mediaParent)->create(['name' => 'Audio']);
    $videoCategory = Category::factory()->withParent($mediaParent)->create(['name' => 'Video']);

    // Create content
    Audio::factory()->create(['category_id' => $audioCategory->id]);
    Video::factory()->create(['category_id' => $videoCategory->id]);

    // Test hierarchical filtering
    expect($mediaParent->subcategories)->toHaveCount(2);
    expect($audioCategory->parent->id)->toBe($mediaParent->id);
    expect($videoCategory->parent->id)->toBe($mediaParent->id);

    // Test content filtering by category
    expect(Audio::inCategory($audioCategory->id)->count())->toBe(1);
    expect(Video::inCategory($videoCategory->id)->count())->toBe(1);
    expect(Video::inCategory($audioCategory->id)->count())->toBe(0);
});

test('youtube url processing in real workflow', function () {
    $category = Category::factory()->create();

    // Simulate form submission with YouTube URL
    $youtubeUrl = 'https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=30s';
    $extractedId = Video::extractVideoId($youtubeUrl);

    expect($extractedId)->toBe('dQw4w9WgXcQ');
    expect(Video::isValidYouTubeUrl($youtubeUrl))->toBeTrue();

    // Create video with extracted ID
    $video = Video::create([
        'title' => 'Test Video',
        'description' => 'Test Description',
        'youtube_video_id' => $extractedId,
        'duration' => '3:33',
        'category_id' => $category->id,
    ]);

    expect($video->youtube_video_id)->toBe('dQw4w9WgXcQ');
    expect($video->embed_url)->toBe('https://www.youtube.com/embed/dQw4w9WgXcQ');
});
