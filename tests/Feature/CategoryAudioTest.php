<?php

use App\Enums\CategoryType;
use App\Models\Audio;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('category can have multiple audio files', function () {
    $category = Category::factory()->create([
        'name' => 'Test Category',
        'status' => true,
    ]);

    $audio1 = Audio::factory()->create(['category_id' => $category->id]);
    $audio2 = Audio::factory()->create(['category_id' => $category->id]);

    expect($category->audio)->toHaveCount(2);
    expect($category->audio->first()->id)->toBe($audio1->id);
    expect($category->audio->last()->id)->toBe($audio2->id);
});

test('audio belongs to a category', function () {
    $category = Category::factory()->create();
    $audio = Audio::factory()->create(['category_id' => $category->id]);

    expect($audio->category->id)->toBe($category->id);
    expect($audio->category->name)->toBe($category->name);
});

test('category scopes work correctly', function () {
    Category::factory()->create(['status' => true]);
    Category::factory()->create(['status' => false]);
    Category::factory()->create(['name' => 'Audio Category']);
    Category::factory()->create(['name' => 'Video Category']);

    expect(Category::active()->count())->toBeGreaterThanOrEqual(1);
});

test('audio scopes work correctly', function () {
    $category = Category::factory()->create();
    Audio::factory()->create(['category_id' => $category->id]);

    expect(Audio::inCategory($category->id)->count())->toBe(1);
});

test('enum provides correct labels and colors', function () {
    expect(CategoryType::Audio->getLabel())->toBe(__('audios'));
    expect(CategoryType::Video->getLabel())->toBe(__('videos'));
    expect(CategoryType::Reference->getLabel())->toBe(__('references'));
    expect(CategoryType::Tweet->getLabel())->toBe(__('tweets'));

    expect(CategoryType::Audio->getColor())->toBe('primary');
    expect(CategoryType::Video->getColor())->toBe('success');
    expect(CategoryType::Reference->getColor())->toBe('warning');
    expect(CategoryType::Tweet->getColor())->toBe('danger');
});

test('enum helper methods work correctly', function () {
    $values = CategoryType::values();
    expect($values)->toContain('audio', 'video', 'reference', 'tweet');

    $options = CategoryType::options();
    expect($options)->toHaveKey('audio');
    expect($options)->toHaveKey('video');
    expect($options)->toHaveKey('reference');
    expect($options)->toHaveKey('tweet');

    expect(CategoryType::fromValue('audio'))->toBe(CategoryType::Audio);
    expect(CategoryType::fromValue('invalid'))->toBeNull();
});
